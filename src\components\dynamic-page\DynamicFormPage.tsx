import { useEffect, useState } from "react";
import { FormProvider } from "react-hook-form";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { AlertCircle } from "lucide-react";
import {
  FormPageConfig,
  convertToFormComponents,
} from "@/lib/types/page-config";
import { useDynamicForm } from "@/hooks/useDynamicForm";
import { EntityService } from "@/lib/services/entity-service";
import DynamicPageHeader from "./DynamicPageHeader";
import RenderComponent from "@/components/form-builder/form-components/RenderComponent";
import FormStatusMessage from "@/components/form-builder/form-components/FormStatusMessage";
import StepProgress from "@/components/form-builder/form-components/StepProgress";
import StepNavigation from "@/components/form-builder/form-components/StepNavigation";
import { evaluateConditionalRendering } from "@/lib/utils/component-utils";
import { createComponentValidationSchema } from "@/lib/utils/zod-validation-utils";
import {
  formatDateForInput,
  formatDateTimeForInput,
} from "@/lib/utils/date-utils";
import { Loading } from "@/components/ui/loading";

interface DynamicFormPageProps {
  config: FormPageConfig;
  entityId?: string;
  initialData?: Record<string, any>;
  onSuccess?: (data: Record<string, any>) => void;
  onCancel?: () => void;
  backButtonUrl?: string;
}

/**
 * Component for rendering a dynamic form page
 */
export function DynamicFormPage({
  config,
  entityId,
  initialData: providedInitialData,
  onSuccess,
  onCancel,
  backButtonUrl,
}: DynamicFormPageProps) {
  const [initialData, setInitialData] = useState<Record<string, any>>(
    providedInitialData || {}
  );
  const [isLoadingData, setIsLoadingData] = useState(
    !!entityId && !providedInitialData
  );

  // Load entity data if entityId is provided and no initialData was passed
  useEffect(() => {
    const loadEntityData = async () => {
      // Skip loading if initialData was provided via props
      if (providedInitialData) {
        setInitialData(providedInitialData);
        setIsLoadingData(false);
        return;
      }

      if (entityId) {
        try {
          setIsLoadingData(true);
          const data = await EntityService.getEntityById(
            config.entityName,
            config.endpoints.get,
            entityId
          );
          if (data) {
            setInitialData(data);
          } else {
            console.warn(
              `No data found for ${config.entityName} with ID: ${entityId}`
            );
          }
        } catch (error) {
          console.error("Error loading entity data:", error);
        } finally {
          setIsLoadingData(false);
        }
      } else {
        // Reset initial data when creating a new entity
        setInitialData({});
      }
    };

    loadEntityData();
  }, [config.entityName, entityId, providedInitialData]);

  // Validate that the config has fields
  if (
    !config.fields ||
    !Array.isArray(config.fields) ||
    config.fields.length === 0
  ) {
    console.error("Invalid form configuration: No fields defined");
  }

  // Use dynamic form hook
  const {
    methods,
    isSubmitting,
    formStatus,
    onSubmit,
    onCancel: handleCancel,
    steps,
    isMultiStep,
    currentStep,
    nextStep,
    prevStep,
  } = useDynamicForm({
    config,
    entityId,
    initialData,
    onSuccess,
    onError: (error) => console.error("Form submission error:", error),
  });

  // Extract methods from react-hook-form
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = methods;

  // Reset form when initialData changes
  useEffect(() => {
    if (Object.keys(initialData).length > 0) {
      // Create a copy of initialData to modify date fields
      const formattedData = { ...initialData };

      // Format date and datetime fields for input elements
      config.fields.forEach((field) => {
        if (field.type === "date" && formattedData[field.name]) {
          // Convert ISO date string to YYYY-MM-DD format for date inputs
          formattedData[field.name] = formatDateForInput(
            formattedData[field.name]
          );
        } else if (field.type === "datetime" && formattedData[field.name]) {
          // Convert ISO date string to YYYY-MM-DDThh:mm format for datetime-local inputs
          formattedData[field.name] = formatDateTimeForInput(
            formattedData[field.name]
          );
        }
      });

      // First reset the form with all formatted data
      reset(formattedData);

      // Then explicitly set each field value to ensure they're properly applied
      // This is especially important for select fields
      Object.entries(formattedData).forEach(([key, value]) => {
        if (value !== undefined) {
          setValue(key, value);
        }
      });
    }
  }, [initialData, reset, setValue, config.fields]);

  // Get current step data
  const currentStepData = steps[currentStep];

  // Convert field configs to form components
  const formComponents = convertToFormComponents(currentStepData.fields);

  // Handle cancel button click
  const handleCancelClick = () => {
    if (onCancel) {
      onCancel();
    } else {
      handleCancel();
    }
  };

  if (isLoadingData) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      <DynamicPageHeader
        config={config}
        showBackButton={false}
      // backButtonUrl={backButtonUrl}
      // backButtonText="Back"
      />

      <Card>
        {isMultiStep && (
          <StepProgress
            currentStep={currentStep}
            totalSteps={steps.length}
            steps={steps.map((step) => ({
              label: step.label,
              description: step.description,
            }))}
          />
        )}

        <CardContent className="pt-6">
          <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <FormStatusMessage
                isSubmitted={formStatus.isSubmitted}
                isValid={formStatus.isValid}
                message={formStatus.message}
              />

              {formComponents.map((component) => {
                // Check if this component should be rendered based on conditional rules
                const shouldRender = evaluateConditionalRendering(
                  component,
                  watch
                );

                if (!shouldRender) {
                  return null; // Skip rendering this component
                }

                return (
                  <div key={component.id} className="space-y-2">
                    <div className="space-y-1">
                      {component.type !== "step" &&
                        component.type !== "section" &&
                        component.type !== "infoText" && (
                          <Label
                            htmlFor={component.id}
                            className="flex items-center gap-1"
                          >
                            {component.label}
                            {component.required && (
                              <span className="text-destructive">*</span>
                            )}
                          </Label>
                        )}

                      <RenderComponent
                        key={
                          component.name === "status"
                            ? `${component.name}-${watch(component.name)}`
                            : component.id
                        }
                        component={{
                          ...component,
                          defaultValue: watch(component.name),
                        }}
                        register={register}
                        control={control}
                        errors={errors}
                        setValue={setValue}
                        watch={watch}
                        validationRules={createComponentValidationSchema(
                          component
                        ).getValidationRules()}
                        allComponents={formComponents}
                        mode="edit"
                      />

                      {errors[component.name] && (
                        <div className="text-sm text-destructive flex items-center gap-1 mt-1">
                          <AlertCircle className="h-4 w-4" />
                          <span>
                            {errors[component.name]?.message as string}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}

              {isMultiStep ? (
                <StepNavigation
                  currentStep={currentStep}
                  totalSteps={steps.length}
                  onNext={nextStep}
                  onPrevious={prevStep}
                  isMultiStep={isMultiStep}
                  onSubmit={handleSubmit(onSubmit)}
                />
              ) : (
                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancelClick}
                    disabled={isSubmitting}
                  >
                    {config.cancelButtonText || "Cancel"}
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {config.submitButtonText ||
                      (entityId ? "Update" : "Create")}
                  </Button>
                </div>
              )}
            </form>
          </FormProvider>
        </CardContent>

        <CardFooter className="border-t px-6 py-4 bg-muted/50">
          <p className="text-sm text-muted-foreground">
            {entityId
              ? `Editing ${config.entityName} with ID: ${entityId}`
              : `Creating a new ${config.entityName}`}
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}

export default DynamicFormPage;
